import React from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

// Types from the original Vue component
export interface DateTimeValue {
  day_of_month: number;
  day_of_week: number;
  hour: number;
  minutes: number;
  month: number;
  seconds: number;
  year: number;
}

export interface DeSettingsValue {
  auction_name: string;
  cost_multiplier: string;
  excess_level_0_label: string;
  excess_level_1_label: string;
  excess_level_1_quantity: string;
  excess_level_2_label: string;
  excess_level_2_quantity: string;
  excess_level_3_label: string;
  excess_level_3_quantity: string;
  excess_level_4_label: string;
  excess_level_4_quantity: string;
  price_change_initial: string;
  price_change_post_reversal: string;
  price_decimal_places: number;
  price_label: string;
  quantity_label: string;
  quantity_minimum: string;
  quantity_step: string;
  round_closed_min_secs: number;
  round_open_min_secs: number;
  round_orange_secs: number;
  round_red_secs: number;
  starting_price_announcement_mins: number;
  starting_time: DateTimeValue | null;
  default_buyer_credit_limit?: number;
  default_seller_quantity_limit?: string;
  category_id?: string;
}

export enum Crud {
  CREATE = 'CREATE',
  READ = 'READ',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  ADD = 'ADD',
  REMOVE = 'REMOVE',
  CLEAR = 'CLEAR'
}

export enum PriceDirection {
  UP = 'UP',
  DOWN = 'DOWN'
}

interface AuctionCategory {
  id: string;
  name: string;
}

export interface AuctionSettingsProps {
  settings: DeSettingsValue;
  crud: Crud;
  onSettingsChange: (settings: DeSettingsValue) => void;
  auctionCategories?: AuctionCategory[];
}

// Helper functions for date conversion
const dateTimeValueToDate = (dtv: DateTimeValue | null): Date | undefined => {
  if (!dtv) return undefined;
  return new Date(dtv.year, dtv.month, dtv.day_of_month, dtv.hour, dtv.minutes, dtv.seconds);
};

const dateToDateTimeValue = (date: Date | undefined): DateTimeValue | null => {
  if (!date) return null;
  return {
    day_of_month: date.getDate(),
    day_of_week: date.getDay(),
    hour: date.getHours(),
    minutes: date.getMinutes(),
    month: date.getMonth(),
    seconds: date.getSeconds(),
    year: date.getFullYear(),
  };
};

const isCrudEditable = (crud: Crud): boolean => {
  return crud === Crud.CREATE || crud === Crud.UPDATE;
};

export function AuctionSettings({
  settings,
  crud,
  onSettingsChange,
  auctionCategories = []
}: AuctionSettingsProps) {
  const editable = isCrudEditable(crud);

  const updateSettings = (updates: Partial<DeSettingsValue>) => {
    onSettingsChange({ ...settings, ...updates });
  };

  const updateDecimalPlaces = (value: number) => {
    const forceDecimals = (str: string, decimals: number): string => {
      const num = parseFloat(str) || 0;
      return num.toFixed(decimals);
    };

    updateSettings({
      price_decimal_places: value,
      price_change_initial: forceDecimals(settings.price_change_initial, value),
      price_change_post_reversal: forceDecimals(settings.price_change_post_reversal, value),
    });
  };

  // Removed unused priceDirectionOptions - can be added back when needed

  const minExcessValue = settings.excess_level_1_quantity;

  return (
    <div className="auction-settings bg-gray-900 border border-gray-800 rounded-md p-2 text-xs w-[555px] text-white">
      {/* Auction Name */}
      <div className="legend text-gray-300 font-bold text-sm mb-2 ml-1">Auction Name</div>
      <Textarea
        className="h-10 w-[550px] text-xs bg-gray-800 border-gray-600 text-white"
        rows={2}
        value={settings.auction_name}
        onChange={(e) => updateSettings({ auction_name: e.target.value })}
        disabled={!editable}
      />

      <hr className="my-3 border-gray-700" />

      {/* Round Timer */}
      <div className="legend text-gray-300 font-bold text-sm mb-2 ml-1">Round Timer</div>

      <div className="form-item flex items-baseline mb-2">
        <label className="label text-gray-300 font-bold min-w-[250px] text-right w-[250px]">
          Starting time:&nbsp;
        </label>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "input text-xs h-[23px] mx-2 w-[100px] justify-start text-left font-normal bg-gray-800 border-gray-600 text-white",
                !settings.starting_time && "text-muted-foreground"
              )}
              disabled={!editable}
            >
              <CalendarIcon className="mr-2 h-3 w-3" />
              {settings.starting_time ? (
                format(dateTimeValueToDate(settings.starting_time)!, "MM/dd/yyyy 'at' hh:mm a")
              ) : (
                <span>Pick a date</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={dateTimeValueToDate(settings.starting_time)}
              onSelect={(date) => updateSettings({ starting_time: dateToDateTimeValue(date) })}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      <div className="form-item flex items-baseline mb-2">
        <label className="label text-gray-300 font-bold min-w-[250px] text-right w-[250px]">
          Mins before start to announce price:&nbsp;
        </label>
        <Input
          type="number"
          className="input text-xs h-[23px] mx-2 w-[100px] bg-gray-800 border-gray-600 text-white"
          value={settings.starting_price_announcement_mins}
          onChange={(e) => updateSettings({ starting_price_announcement_mins: parseInt(e.target.value) || 0 })}
          disabled={!editable}
        />
        <div className="right-column-label text-gray-300 font-bold">(minutes)</div>
      </div>

      <div className="form-item flex items-baseline mb-2">
        <label className="label text-gray-300 font-bold min-w-[250px] text-right w-[250px]">
          Show orange status after:&nbsp;
        </label>
        <Input
          type="number"
          className="input text-xs h-[23px] mx-2 w-[100px] bg-gray-800 border-gray-600 text-white"
          value={settings.round_orange_secs}
          onChange={(e) => updateSettings({ round_orange_secs: parseInt(e.target.value) || 0 })}
          disabled={!editable}
        />
        <div className="right-column-label text-gray-300 font-bold">(seconds)</div>
      </div>

      <div className="form-item flex items-baseline mb-2">
        <label className="label text-gray-300 font-bold min-w-[250px] text-right w-[250px]">
          Show red status after:&nbsp;
        </label>
        <Input
          type="number"
          className="input text-xs h-[23px] mx-2 w-[100px] bg-gray-800 border-gray-600 text-white"
          value={settings.round_red_secs}
          onChange={(e) => updateSettings({ round_red_secs: parseInt(e.target.value) || 0 })}
          disabled={!editable}
        />
        <div className="right-column-label text-gray-300 font-bold">(seconds)</div>
      </div>

      <hr className="my-3 border-gray-700" />

      {/* Price Settings */}
      <div className="legend text-gray-300 font-bold text-sm mb-2 ml-1">Price settings</div>

      <div className="form-item flex items-baseline mb-2">
        <label className="label text-gray-300 font-bold min-w-[250px] text-right w-[250px]">
          Initial price change:
        </label>
        <Input
          type="number"
          step={`0.${'0'.repeat(Math.max(0, settings.price_decimal_places - 1))}1`}
          className="input text-xs h-[23px] mx-2 w-[100px] bg-gray-800 border-gray-600 text-white"
          value={settings.price_change_initial}
          onChange={(e) => updateSettings({ price_change_initial: e.target.value })}
          disabled={!editable}
        />
        <div className="right-column-label text-gray-300 font-bold">({settings.price_label})</div>
      </div>

      <div className="form-item flex items-baseline mb-2">
        <label className="label text-gray-300 font-bold min-w-[250px] text-right w-[250px]">
          Post reversal price change:
        </label>
        <Input
          type="number"
          step={`0.${'0'.repeat(Math.max(0, settings.price_decimal_places - 1))}1`}
          className="input text-xs h-[23px] mx-2 w-[100px] bg-gray-800 border-gray-600 text-white"
          value={settings.price_change_post_reversal}
          onChange={(e) => updateSettings({ price_change_post_reversal: e.target.value })}
          disabled={!editable}
        />
        <div className="right-column-label text-gray-300 font-bold">({settings.price_label})</div>
      </div>

      <div className="form-item flex items-baseline mb-2">
        <label className="label text-gray-300 font-bold min-w-[250px] text-right w-[250px]">
          Price label:&nbsp;
        </label>
        <Input
          className="input text-xs h-[23px] mx-2 w-[100px] bg-gray-800 border-gray-600 text-white text-left"
          value={settings.price_label}
          onChange={(e) => updateSettings({ price_label: e.target.value })}
          disabled={!editable}
        />
      </div>

      <div className="form-item flex items-baseline mb-2">
        <label className="label text-gray-300 font-bold min-w-[250px] text-right w-[250px]">
          Decimal places:&nbsp;
        </label>
        <Input
          type="number"
          className="input text-xs h-[23px] mx-2 w-[100px] bg-gray-800 border-gray-600 text-white"
          value={settings.price_decimal_places}
          onChange={(e) => updateDecimalPlaces(parseInt(e.target.value) || 0)}
          disabled={!editable}
        />
        <div className="right-column-label text-gray-300 font-bold">e.g. enter 3 for 0.000</div>
      </div>

      <hr className="my-3 border-gray-700" />

      {/* Quantity Settings */}
      <div className="legend text-gray-300 font-bold text-sm mb-2 ml-1">Quantity Settings</div>

      <div className="form-item flex items-baseline mb-2">
        <label className="label text-gray-300 font-bold min-w-[250px] text-right w-[250px]">
          Category:&nbsp;
        </label>
        <Select
          value={settings.category_id || ''}
          onValueChange={(value) => updateSettings({ category_id: value })}
          disabled={!editable}
        >
          <SelectTrigger className="input text-xs h-[23px] mx-2 w-[195px] bg-gray-800 border-gray-600 text-white">
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            {auctionCategories.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="form-item flex items-baseline mb-2">
        <label className="label text-gray-300 font-bold min-w-[250px] text-right w-[250px]">
          Quantity label:
        </label>
        <Input
          className="input text-xs h-[23px] mx-2 w-[100px] bg-gray-800 border-gray-600 text-white text-left"
          value={settings.quantity_label}
          onChange={(e) => updateSettings({ quantity_label: e.target.value })}
          disabled={!editable}
        />
      </div>

      <div className="form-item flex items-baseline mb-2">
        <label className="label text-gray-300 font-bold min-w-[250px] text-right w-[250px]">
          Quantity decrement:
        </label>
        <Input
          type="number"
          className="input text-xs h-[23px] mx-2 w-[100px] bg-gray-800 border-gray-600 text-white"
          value={settings.quantity_step}
          onChange={(e) => updateSettings({ quantity_step: e.target.value })}
          disabled={!editable}
        />
        <div className="right-column-label text-gray-300 font-bold">({settings.quantity_label})</div>
      </div>

      <div className="form-item flex items-baseline mb-2">
        <label className="label text-gray-300 font-bold min-w-[250px] text-right w-[250px]">
          Minimum Quantity:
        </label>
        <Input
          type="number"
          className="input text-xs h-[23px] mx-2 w-[100px] bg-gray-800 border-gray-600 text-white"
          value={settings.quantity_minimum}
          onChange={(e) => updateSettings({ quantity_minimum: e.target.value })}
          disabled={!editable}
        />
        <div className="right-column-label text-gray-300 font-bold">({settings.quantity_label})</div>
      </div>

      <hr className="my-3 border-gray-700" />

      {/* Default Initial Trader Limits */}
      <div className="legend text-gray-300 font-bold text-sm mb-2 ml-1">Default Initial Trader Limits</div>

      <div className="form-item flex items-baseline mb-2">
        <label className="label text-gray-300 font-bold min-w-[250px] text-right w-[250px]">
          Default seller quantity limit:
        </label>
        <Input
          type="number"
          className="input text-xs h-[23px] mx-2 w-[100px] bg-gray-800 border-gray-600 text-white"
          value={settings.default_seller_quantity_limit || ''}
          onChange={(e) => updateSettings({ default_seller_quantity_limit: e.target.value })}
          disabled={!editable}
        />
        <div className="right-column-label text-gray-300 font-bold">({settings.quantity_label})</div>
      </div>

      <div className="form-item flex items-baseline mb-2">
        <label className="label text-gray-300 font-bold min-w-[250px] text-right w-[250px]">
          Default buyer credit limit
        </label>
        <Input
          type="number"
          className="input text-xs h-[23px] mx-2 w-[100px] bg-gray-800 border-gray-600 text-white"
          value={settings.default_buyer_credit_limit || ''}
          onChange={(e) => updateSettings({ default_buyer_credit_limit: parseInt(e.target.value) || 0 })}
          disabled={!editable}
        />
        <div className="right-column-label text-gray-300 font-bold">
          ($) (determines max buy)
        </div>
      </div>

      <hr className="my-3 border-gray-700" />

      {/* Excess Levels Table */}
      <div className="legend text-gray-300 font-bold text-sm mb-2 ml-1">
        Excess ({settings.quantity_label}) → Label
      </div>

      <div className="ml-[120px]">
        <table className="w-full">
          <thead>
            <tr className="text-center">
              <th className="text-gray-300 p-1">Excess ({settings.quantity_label})</th>
              <th className="text-gray-300 p-1">Excess label</th>
            </tr>
          </thead>
          <tbody>
            {[4, 3, 2, 1].map((n) => (
              <tr key={n}>
                <td className="text-right">
                  <label className="label text-gray-300 font-bold">
                    &gt;
                    <Input
                      type="number"
                      className="input text-xs h-[23px] mx-2 w-[100px] bg-gray-800 border-gray-600 text-white inline-block"
                      value={settings[`excess_level_${n}_quantity` as keyof DeSettingsValue] as string}
                      onChange={(e) => updateSettings({ [`excess_level_${n}_quantity`]: e.target.value })}
                      disabled={!editable}
                    />
                  </label>
                </td>
                <td>
                  <div className="form-item">
                    <Input
                      className="input text-xs h-[23px] mx-2 w-[100px] bg-gray-800 border-gray-600 text-white"
                      value={settings[`excess_level_${n}_label` as keyof DeSettingsValue] as string}
                      onChange={(e) => updateSettings({ [`excess_level_${n}_label`]: e.target.value })}
                      disabled={!editable}
                    />
                  </div>
                </td>
              </tr>
            ))}
            <tr>
              <td className="text-right">
                <label className="label text-gray-300 font-bold">
                  <Input
                    className="input text-xs h-[23px] mx-2 w-[100px] bg-gray-800 border-gray-600 text-white inline-block"
                    value={`0 - ${minExcessValue}`}
                    disabled
                  />
                </label>
              </td>
              <td>
                <div className="form-item">
                  <Input
                    className="input text-xs h-[23px] mx-2 w-[100px] bg-gray-800 border-gray-600 text-white"
                    value={settings.excess_level_0_label}
                    onChange={(e) => updateSettings({ excess_level_0_label: e.target.value })}
                    disabled={!editable}
                  />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
}

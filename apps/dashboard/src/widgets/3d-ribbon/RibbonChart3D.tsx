/**
 * 3D Ribbon Chart component using Plotly.js
 * Visualizes auction bidding data as 3D ribbons showing trader quantity progression across rounds
 */

import React, { useMemo } from 'react';
import Plot from 'react-plotly.js';
import type { DeRoundTraderElement, DeTraderElement } from '@/api-client';
import { transformToRibbonData, getTraderLabels, validateMonotonicConstraints } from './ribbon-data-transform';

export interface RibbonChart3DProps {
  width: number;
  height: number;
  round_trader_elements: DeRoundTraderElement[];
  traders: DeTraderElement[];
  title?: string;
  ribbonWidth?: number;
  singleColor?: boolean;
  showValidation?: boolean;
}

export const RibbonChart3D: React.FC<RibbonChart3DProps> = ({
  width,
  height,
  round_trader_elements,
  traders,
  title = '3D Auction Bidding Ribbons',
  ribbonWidth = 0.8,
  singleColor = false,
  showValidation = false
}) => {
  // Transform data to Plotly format
  const { surfaces, layout: baseLayout } = useMemo(() => 
    transformToRibbonData(round_trader_elements, traders, { ribbonWidth, singleColor }),
    [round_trader_elements, traders, ribbonWidth, singleColor]
  );

  // Get trader labels for better Y-axis display
  const traderLabels = useMemo(() => 
    getTraderLabels(round_trader_elements, traders),
    [round_trader_elements, traders]
  );

  // Validate monotonic constraints if requested
  const validation = useMemo(() => 
    showValidation ? validateMonotonicConstraints(round_trader_elements) : null,
    [round_trader_elements, showValidation]
  );

  // Plotly layout configuration
  const layout = {
    title: {
      text: title,
      font: { size: 16 }
    },
    width,
    height,
    margin: { l: 50, r: 50, t: 80, b: 50 },
    scene: {
      ...baseLayout.scene,
      camera: {
        eye: { x: 1.5, y: 1.5, z: 1.2 },
        center: { x: 0, y: 0, z: 0 },
        up: { x: 0, y: 0, z: 1 }
      },
      aspectratio: { x: 1, y: 1, z: 0.8 },
      xaxis: {
        title: 'Round Number',
        titlefont: { size: 14 },
        tickfont: { size: 12 }
      },
      yaxis: {
        title: 'Traders',
        titlefont: { size: 14 },
        tickfont: { size: 12 },
        // Use trader labels if available
        ...(traderLabels.length > 0 && {
          tickmode: 'array',
          tickvals: traderLabels.map((_, i) => i),
          ticktext: traderLabels
        })
      },
      zaxis: {
        title: 'Quantity (+Buy, -Sell)',
        titlefont: { size: 14 },
        tickfont: { size: 12 }
      }
    },
    showlegend: false,
    paper_bgcolor: 'white',
    plot_bgcolor: 'white'
  };

  // Plotly configuration
  const config = {
    displayModeBar: true,
    displaylogo: false,
    modeBarButtonsToRemove: [
      'pan2d',
      'select2d',
      'lasso2d',
      'resetScale2d',
      'autoScale2d'
    ],
    responsive: true
  };

  return (
    <div style={{ width, height }}>
      {/* Validation warnings */}
      {validation && validation.summary.totalViolations > 0 && (
        <div style={{ 
          marginBottom: '10px', 
          padding: '8px', 
          backgroundColor: '#fff3cd', 
          border: '1px solid #ffeaa7',
          borderRadius: '4px',
          fontSize: '12px'
        }}>
          <strong>Monotonic Constraint Violations:</strong> {validation.summary.totalViolations} 
          ({validation.summary.buyViolations} buy, {validation.summary.sellViolations} sell)
        </div>
      )}
      
      {/* 3D Plot */}
      <Plot
        data={surfaces}
        layout={layout}
        config={config}
        style={{ width: '100%', height: '100%' }}
        useResizeHandler={true}
      />
    </div>
  );
};

export default RibbonChart3D;
